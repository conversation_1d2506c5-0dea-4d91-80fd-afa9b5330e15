/*
 * @Date: 2025-10-05
 * @Description: Advanced readable code generator for Cocos Creator reverse engineering
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse');
const types = require('@babel/types');
const generate = require('@babel/generator');
const { logger } = require('../utils/logger');
const { PatternRecognitionSystem } = require('./patternRecognition');
const { AdvancedTypeInference } = require('./typeInference');
const { AdvancedDocumentationGenerator } = require('./documentationGenerator');
const { SpecializedTransformers } = require('./specializedTransformers');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

/**
 * Advanced readable code generator
 */
class ReadableCodeGenerator {
    constructor() {
        this.patternRecognition = new PatternRecognitionSystem();
        this.typeInference = new AdvancedTypeInference();
        this.documentationGenerator = new AdvancedDocumentationGenerator();
        this.specializedTransformers = new SpecializedTransformers();
    }

    /**
     * Initialize Cocos Creator patterns for recognition
     */
    initializeCocosPatterns() {
        // Component patterns
        this.cocosPatterns.set('ccclass', {
            type: 'decorator',
            generates: 'component',
            imports: ['_decorator', 'Component']
        });

        this.cocosPatterns.set('property', {
            type: 'decorator',
            generates: 'property',
            imports: ['_decorator']
        });

        // Common Cocos types
        this.cocosPatterns.set('Sprite', {
            type: 'class',
            imports: ['Sprite']
        });

        this.cocosPatterns.set('SpriteFrame', {
            type: 'class',
            imports: ['SpriteFrame']
        });

        this.cocosPatterns.set('Node', {
            type: 'class',
            imports: ['Node']
        });

        this.cocosPatterns.set('Vec3', {
            type: 'class',
            imports: ['Vec3']
        });

        this.cocosPatterns.set('tween', {
            type: 'function',
            imports: ['tween']
        });
    }

    /**
     * Transform minified code to readable TypeScript
     * @param {string} minifiedCode - The minified JavaScript code
     * @param {string} fileName - Original file name for context
     * @param {Object} options - Configuration options
     * @returns {Promise<string>} - Readable TypeScript code
     */
    async transformToReadable(minifiedCode, fileName = 'Component', options = {}) {
        // Merge with global config options
        const config = {
            generateInterfaces: true,
            addDocumentation: true,
            inferTypes: true,
            recognizePatterns: true,
            generateExamples: true,
            ...((global.config && global.config.output && global.config.output.readableCodeOptions) || {}),
            ...options
        };
        try {
            logger.debug(`Transforming ${fileName} to readable code...`);

            // Check for specialized transformers first
            const specializedTransformer = this.specializedTransformers.needsSpecializedTransform(minifiedCode, fileName);
            if (specializedTransformer) {
                const specializedCode = this.specializedTransformers.transform(specializedTransformer, minifiedCode, fileName);
                if (specializedCode) {
                    return specializedCode;
                }
            }

            // 1. Parse the minified code
            const ast = this.parseMinifiedCode(minifiedCode);

            // 2. Analyze and extract patterns (if enabled)
            const analysis = config.recognizePatterns ?
                this.analyzeCodeStructure(ast, fileName) :
                this.createBasicAnalysis(fileName);

            // 3. Transform to readable structure
            const readableAst = this.transformAst(ast, analysis);

            // 4. Generate TypeScript code
            const readableCode = this.generateTypeScriptCode(readableAst, analysis, config);

            // 5. Add documentation and formatting (if enabled)
            const finalCode = config.addDocumentation ?
                this.addDocumentationAndFormatting(readableCode, analysis) :
                readableCode;

            logger.debug(`Successfully transformed ${fileName}`);
            return finalCode;

        } catch (error) {
            logger.error(`Error transforming ${fileName}:`, error);
            return this.generateFallbackCode(minifiedCode, fileName);
        }
    }

    /**
     * Parse minified code with error handling
     * @param {string} code - Minified code
     * @returns {Object} - AST
     */
    parseMinifiedCode(code) {
        const parserOptions = {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators',
                'classProperties',
                'dynamicImport',
                'objectRestSpread',
                'optionalChaining',
                'nullishCoalescingOperator'
            ]
        };

        try {
            return parser.parse(code, parserOptions);
        } catch (error) {
            // Try with different parser options if first attempt fails
            logger.warn('Initial parse failed, trying alternative parser options...');
            parserOptions.sourceType = 'script';
            parserOptions.plugins = ['decorators-legacy'];
            return parser.parse(code, parserOptions);
        }
    }

    /**
     * Create basic analysis when pattern recognition is disabled
     * @param {string} fileName - File name for context
     * @returns {Object} - Basic analysis results
     */
    createBasicAnalysis(fileName) {
        return {
            fileName,
            className: this.extractClassName(fileName),
            imports: new Set(['_decorator', 'Component']),
            properties: [],
            methods: [],
            decorators: [],
            interfaces: [],
            componentType: 'Component',
            hasAnimations: false,
            hasSprites: false,
            hasEvents: false,
            metadata: {
                isCardGame: false,
                hasAnimations: false,
                hasSprites: false,
                hasEvents: false,
                hasLogging: false
            }
        };
    }

    /**
     * Analyze code structure and extract meaningful information
     * @param {Object} ast - Abstract syntax tree
     * @param {string} fileName - File name for context
     * @returns {Object} - Analysis results
     */
    analyzeCodeStructure(ast, fileName) {
        const analysis = {
            fileName,
            className: this.extractClassName(fileName),
            imports: new Set(),
            properties: [],
            methods: [],
            decorators: [],
            interfaces: [],
            componentType: 'Component',
            hasAnimations: false,
            hasSprites: false,
            hasEvents: false
        };

        // Traverse AST to extract information
        traverse.default(ast, {
            // Detect class declarations
            ClassDeclaration(path) {
                const { node } = path;
                if (node.id && node.id.name) {
                    analysis.className = node.id.name;
                }
                
                // Check for Component inheritance
                if (node.superClass && node.superClass.name === 'Component') {
                    analysis.componentType = 'Component';
                    analysis.imports.add('Component');
                }
            },

            // Detect method definitions
            ClassMethod(path) {
                const { node } = path;
                if (node.key && node.key.name) {
                    const methodInfo = {
                        name: node.key.name,
                        type: this.inferMethodType(node.key.name),
                        parameters: node.params.map(param => ({
                            name: param.name || 'param',
                            type: this.typeInference.inferParameterType(param)
                        }))
                    };
                    analysis.methods.push(methodInfo);
                }
            },

            // Detect property assignments
            AssignmentExpression(path) {
                const { node } = path;
                if (types.isMemberExpression(node.left) && 
                    types.isThisExpression(node.left.object)) {
                    const propertyName = node.left.property.name;
                    if (propertyName && !propertyName.startsWith('_')) {
                        const propertyInfo = {
                            name: propertyName,
                            type: this.typeInference.inferPropertyType(node.right),
                            isPublic: true
                        };
                        analysis.properties.push(propertyInfo);
                    }
                }
            },

            // Detect function calls that indicate features
            CallExpression(path) {
                const { node } = path;
                if (types.isIdentifier(node.callee)) {
                    const functionName = node.callee.name;
                    
                    if (functionName === 'tween') {
                        analysis.hasAnimations = true;
                        analysis.imports.add('tween');
                        analysis.imports.add('Vec3');
                    }
                    
                    if (functionName.includes('Sprite')) {
                        analysis.hasSprites = true;
                        analysis.imports.add('Sprite');
                        analysis.imports.add('SpriteFrame');
                    }
                }
            }
        });

        return analysis;
    }

    /**
     * Extract class name from file name
     * @param {string} fileName - File name
     * @returns {string} - Class name
     */
    extractClassName(fileName) {
        const baseName = path.basename(fileName, path.extname(fileName));
        // Convert to PascalCase
        return baseName.charAt(0).toUpperCase() + baseName.slice(1);
    }

    /**
     * Infer method type based on name
     * @param {string} methodName - Method name
     * @returns {string} - Method type
     */
    inferMethodType(methodName) {
        const lifecycleMethods = ['start', 'update', 'onLoad', 'onDestroy', 'onEnable', 'onDisable'];
        const eventMethods = ['onClick', 'onTouch', 'onCollision'];
        
        if (lifecycleMethods.includes(methodName)) {
            return 'lifecycle';
        }
        
        if (eventMethods.some(event => methodName.includes(event))) {
            return 'event';
        }
        
        if (methodName.startsWith('set') || methodName.startsWith('Set')) {
            return 'setter';
        }
        
        if (methodName.startsWith('get') || methodName.startsWith('Get')) {
            return 'getter';
        }
        
        if (methodName.includes('Animation') || methodName.includes('Tween')) {
            return 'animation';
        }
        
        return 'method';
    }

    /**
     * Transform AST to readable structure
     * @param {Object} ast - Original AST
     * @param {Object} analysis - Code analysis
     * @returns {Object} - Transformed AST
     */
    transformAst(ast, analysis) {
        // This is a simplified transformation
        // In a full implementation, this would rebuild the AST with proper structure
        return ast;
    }

    /**
     * Generate TypeScript code from AST
     * @param {Object} ast - Transformed AST
     * @param {Object} analysis - Code analysis
     * @param {Object} config - Configuration options
     * @returns {string} - TypeScript code
     */
    generateTypeScriptCode(ast, analysis, config = {}) {
        // Generate imports
        const imports = this.generateImports(analysis.imports);

        // Generate interfaces if needed and enabled
        const interfaces = config.generateInterfaces ?
            this.generateInterfaces(analysis) : '';

        // Generate class structure
        const classCode = this.generateClassStructure(analysis, config);

        return `${imports}\n\n${interfaces}\n\n${classCode}`;
    }

    /**
     * Generate import statements
     * @param {Set} imports - Required imports
     * @returns {string} - Import statements
     */
    generateImports(imports) {
        const cocosImports = Array.from(imports).filter(imp => 
            ['Component', 'Sprite', 'SpriteFrame', 'Node', 'Vec3', 'tween', 'warn'].includes(imp)
        );
        
        if (cocosImports.length === 0) {
            return `import { _decorator, Component } from 'cc';\n\nconst { ccclass, property } = _decorator;`;
        }
        
        const importList = ['_decorator', ...cocosImports].join(', ');
        return `import { ${importList} } from 'cc';\n\nconst { ccclass, property } = _decorator;`;
    }

    /**
     * Generate interface definitions
     * @param {Object} analysis - Code analysis
     * @returns {string} - Interface code
     */
    generateInterfaces(analysis) {
        // Generate interfaces based on detected patterns
        let interfaces = '';
        
        if (analysis.hasAnimations) {
            interfaces += `// Animation callback interface\ninterface AnimationCallback {\n    (): void;\n}\n\n`;
        }
        
        return interfaces;
    }

    /**
     * Generate class structure
     * @param {Object} analysis - Code analysis
     * @param {Object} config - Configuration options
     * @returns {string} - Class code
     */
    generateClassStructure(analysis, config = {}) {
        const className = analysis.className;
        const properties = config.inferTypes ?
            this.generateProperties(analysis.properties) :
            this.generateBasicProperties();
        const methods = this.generateMethods(analysis.methods, config);

        return `@ccclass('${className}')
export class ${className} extends Component {
${properties}

    start() {
        console.log(\`[\${this.constructor.name}] Component initialized\`);
    }

${methods}
}`;
    }

    /**
     * Generate property declarations
     * @param {Array} properties - Property information
     * @returns {string} - Property code
     */
    generateProperties(properties) {
        return properties.map(prop => {
            const decorator = this.getPropertyDecorator(prop.type);
            return `    ${decorator}\n    ${prop.name}: ${prop.type} = ${this.getDefaultValue(prop.type)};`;
        }).join('\n\n');
    }

    /**
     * Get property decorator based on type
     * @param {string} type - Property type
     * @returns {string} - Decorator
     */
    getPropertyDecorator(type) {
        if (type === 'Sprite' || type === 'SpriteFrame' || type === 'Node') {
            return `@property(${type})`;
        }
        if (type.includes('[]')) {
            const baseType = type.replace('[]', '');
            return `@property([${baseType}])`;
        }
        return '@property';
    }

    /**
     * Get default value for type
     * @param {string} type - Property type
     * @returns {string} - Default value
     */
    getDefaultValue(type) {
        if (type.includes('[]')) return '[]';
        if (type === 'string') return '""';
        if (type === 'number') return '0';
        if (type === 'boolean') return 'false';
        return 'null';
    }

    /**
     * Generate basic properties when type inference is disabled
     * @returns {string} - Basic property code
     */
    generateBasicProperties() {
        return `    // Properties will be added here
    // TODO: Add component properties`;
    }

    /**
     * Generate method implementations
     * @param {Array} methods - Method information
     * @param {Object} config - Configuration options
     * @returns {string} - Method code
     */
    generateMethods(methods, config = {}) {
        return methods.map(method => {
            const params = config.inferTypes ?
                method.parameters.map(p => `${p.name}: ${p.type}`).join(', ') :
                method.parameters.map(p => p.name).join(', ');

            const documentation = config.addDocumentation ?
                this.documentationGenerator.generateMethodDoc(method) :
                `    // Method: ${method.name}`;

            return `    ${documentation}
    ${method.name}(${params}) {
        // TODO: Implement ${method.name}
        console.log('${method.name} called');
    }`;
        }).join('\n\n');
    }

    /**
     * Add documentation and final formatting
     * @param {string} code - Generated code
     * @param {Object} analysis - Code analysis
     * @returns {string} - Final formatted code
     */
    addDocumentationAndFormatting(code, analysis) {
        const header = this.documentationGenerator.generateFileHeader(analysis);
        return `${header}\n\n${code}`;
    }

    /**
     * Generate fallback code when transformation fails
     * @param {string} originalCode - Original minified code
     * @param {string} fileName - File name
     * @returns {string} - Fallback code
     */
    generateFallbackCode(originalCode, fileName) {
        const className = this.extractClassName(fileName);
        
        return `/*
 * ${className} - Game Component
 * Human-readable version generated by cc-reverse
 * Note: Original code could not be fully transformed
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('${className}')
export class ${className} extends Component {

    start() {
        console.log(\`[\${this.constructor.name}] Component initialized\`);
    }

    // Original minified code (needs manual review):
    /*
    ${originalCode.substring(0, 1000)}${originalCode.length > 1000 ? '...' : ''}
    */
}`;
    }
}

/**
 * Type inference engine for determining TypeScript types
 */
class TypeInferenceEngine {
    /**
     * Infer property type from AST node
     * @param {Object} node - AST node
     * @returns {string} - Inferred type
     */
    inferPropertyType(node) {
        if (types.isStringLiteral(node)) return 'string';
        if (types.isNumericLiteral(node)) return 'number';
        if (types.isBooleanLiteral(node)) return 'boolean';
        if (types.isArrayExpression(node)) return 'any[]';
        if (types.isNullLiteral(node)) return 'any | null';
        return 'any';
    }

    /**
     * Infer parameter type
     * @param {Object} param - Parameter node
     * @returns {string} - Inferred type
     */
    inferParameterType(param) {
        // Basic type inference - can be enhanced
        return 'any';
    }
}

/**
 * Documentation generator for JSDoc comments
 */
class DocumentationGenerator {
    /**
     * Generate file header documentation
     * @param {Object} analysis - Code analysis
     * @returns {string} - File header
     */
    generateFileHeader(analysis) {
        return `/*
 * ${analysis.className} - Game Component
 * Human-readable version generated by cc-reverse
 * 
 * This component was reverse-engineered from minified code.
 * ${analysis.hasAnimations ? '* Features: Animation support' : ''}
 * ${analysis.hasSprites ? '* Features: Sprite management' : ''}
 */`;
    }

    /**
     * Generate method documentation
     * @param {Object} method - Method information
     * @returns {string} - JSDoc comment
     */
    generateMethodDoc(method) {
        const description = this.getMethodDescription(method.name, method.type);
        const params = method.parameters.map(p => 
            `     * @param {${p.type}} ${p.name} - Parameter description`
        ).join('\n');
        
        return `    /**
     * ${description}
${params ? params + '\n' : ''}     */`;
    }

    /**
     * Get method description based on name and type
     * @param {string} name - Method name
     * @param {string} type - Method type
     * @returns {string} - Description
     */
    getMethodDescription(name, type) {
        const descriptions = {
            'lifecycle': `Lifecycle method: ${name}`,
            'event': `Event handler: ${name}`,
            'setter': `Set property value`,
            'getter': `Get property value`,
            'animation': `Animation method: ${name}`,
            'method': `Method: ${name}`
        };
        
        return descriptions[type] || `Method: ${name}`;
    }
}

module.exports = { ReadableCodeGenerator };
